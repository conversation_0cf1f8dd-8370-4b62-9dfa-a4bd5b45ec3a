<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\InvitationController;
use App\Http\Controllers\Api\V1\OrganisationController;
use App\Http\Controllers\Api\V1\RoleController;
use App\Http\Controllers\Api\V1\StatusController;
use App\Http\Controllers\Api\V1\SyncController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\UserRoleController;
use App\Services\PermissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Global health check endpoint (outside versioning)
Route::get('/health', [StatusController::class, 'health']);

// API version 1 routes
Route::prefix('v1')->name('api.v1.')->group(function () {
    // Public API routes
    Route::get('/status', [StatusController::class, 'index'])->name('status');
    Route::get('/health', [StatusController::class, 'health'])->name('health');

    // Authentication routes (public)
    Route::prefix('auth')->name('auth.')->group(function () {
        Route::post('/login', [AuthController::class, 'login'])->name('login');
    });

    // Public user registration routes
    Route::prefix('users')->name('users.')->group(function () {
        Route::post('/send-verification-code', [UserController::class, 'sendVerificationCode'])->name('send-verification-code');
        Route::post('/register', [UserController::class, 'register'])->name('register');
    });

    // Invitation routes
    Route::prefix('invitations')->name('invitations.')->group(function () {
        // List invitations - requires authentication and proper permissions
        Route::get('/', [InvitationController::class, 'index'])->name('index')->middleware('auth:sanctum');
        // Show invitation info - can be accessed without authentication, but will return 401 if not logged in
        Route::get('/{invitation}', [InvitationController::class, 'show'])->name('show');
        // Accept invitation - requires authentication
        Route::post('/{invitation}/accept', [InvitationController::class, 'accept'])->name('accept')->middleware('auth:sanctum');
        // Create invitation - requires authentication
        Route::post('/', [InvitationController::class, 'store'])->name('store')->middleware('auth:sanctum');
    });

    // Protected routes (require authentication)
    Route::middleware('auth:sanctum')->group(function () {
        // User information route with roles
        Route::get('/user', [UserController::class, 'getCurrentUser'])->name('user');

        // Authentication management routes
        Route::prefix('auth')->name('auth.')->group(function () {
            Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
            Route::post('/revoke-all-tokens', [AuthController::class, 'revokeAllTokens'])->name('revoke-all-tokens');
        });

        // Organisation management routes
        Route::apiResource('organisations', OrganisationController::class)->except(['destroy']);
        Route::post('/organisations/{organisation}/suspend', [OrganisationController::class, 'suspend'])->name('organisations.suspend');

        // User role management routes (must come before apiResource to avoid conflicts)
        Route::prefix('users')->name('users.')->group(function () {
            // Get assignable roles for current user
            Route::get('/assignable-roles', [UserRoleController::class, 'getAssignableRoles'])->name('assignable-roles');

            // User role operations
            Route::post('/{user}/roles', [UserRoleController::class, 'assignRole'])->name('assign-role');
            Route::delete('/{user}/roles/{role}', [UserRoleController::class, 'removeRole'])->name('remove-role');
            Route::get('/{user}/roles', [UserRoleController::class, 'getUserRoles'])->name('get-roles');

            // Owner role transfer (special case)
            Route::put('/{user}/transfer-owner', [UserRoleController::class, 'transferOwnerRole'])->name('transfer-owner');
        });

        // User management routes - accessible by system admins and organisation owners
        Route::apiResource('users', UserController::class)->except(['destroy']);
        Route::post('/users/{user}/suspend', [UserController::class, 'suspend'])->name('users.suspend');
        Route::post('/users/{user}/activate', [UserController::class, 'activate'])->name('users.activate');

        // User-Organisation association routes
        Route::post('/users/{userId}/organisations/{organisationId}', [UserController::class, 'addToOrganisation'])->name('users.add-to-organisation');
        Route::delete('/users/{userId}/organisations/{organisationId}', [UserController::class, 'removeFromOrganisation'])->name('users.remove-from-organisation');
        Route::put('/users/{userId}/organisations', [UserController::class, 'syncOrganisations'])->name('users.sync-organisations');

        // Role management routes - restricted to Root and Admin roles
        Route::middleware(['admin'])->group(function () {
            Route::get('/roles', [RoleController::class, 'index'])->name('roles.index');
            Route::get('/roles/{role}', [RoleController::class, 'show'])->name('roles.show');
            Route::post('/roles', [RoleController::class, 'store'])->name('roles.store');
            Route::put('/roles/{role}', [RoleController::class, 'update'])->name('roles.update');
            Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->name('roles.destroy');
        });

        // Sync management routes - restricted to system administrators
        Route::prefix('sync')->name('sync.')->group(function () {
            Route::get('/logs', [SyncController::class, 'index'])->name('logs');
            Route::get('/logs/{syncLog}', [SyncController::class, 'show'])->name('show');
            Route::post('/trigger', [SyncController::class, 'trigger'])->name('trigger');
            Route::post('/retry/{syncLog}', [SyncController::class, 'retry'])->name('retry');
            Route::get('/progress', [SyncController::class, 'progress'])->name('progress');
            Route::get('/active-jobs', [SyncController::class, 'activeJobs'])->name('active-jobs');
        });

    });

    // Test route for SQL logging and performance monitoring (only in local environment)
    if (app()->environment('local')) {
        Route::get('/test-sql-logging', function (Request $request) {
            $data = [];
            $testType = $request->query('test', 'basic');

            switch ($testType) {
                case 'performance':
                    // Performance test with multiple queries
                    $data['user_count'] = \App\Models\User::count();
                    $data['verified_users'] = \App\Models\User::whereNotNull('email_verified_at')->count();
                    $data['roles'] = \App\Models\Role::where('guard_name', 'api')->get(['id', 'name', 'organisation_id']);
                    $data['organisations'] = \App\Models\Organisation::all(['id', 'name', 'status']);

                    // Simulate some processing time
                    usleep(50000); // 50ms delay

                    // More complex queries
                    $data['user_roles'] = \App\Models\User::with('roles')->limit(10)->get(['id', 'name', 'email']);
                    $data['recent_users'] = \DB::select('SELECT COUNT(*) as total FROM users WHERE created_at > ?', [now()->subDays(30)])[0]->total ?? 0;
                    break;

                case 'slow':
                    // Simulate slow endpoint
                    $data['message'] = 'Simulating slow endpoint';
                    usleep(200000); // 200ms delay
                    $data['users'] = \App\Models\User::limit(5)->get();
                    usleep(100000); // Additional 100ms delay
                    break;

                case 'heavy':
                    // Heavy database operations
                    $data['all_users'] = \App\Models\User::with(['roles', 'organisations'])->limit(20)->get();
                    $data['all_roles'] = \App\Models\Role::with('users')->get();
                    $data['statistics'] = [
                        'total_users' => \App\Models\User::count(),
                        'total_roles' => \App\Models\Role::count(),
                        'total_orgs' => \App\Models\Organisation::count(),
                    ];
                    break;

                default:
                    // Basic test
                    $data['user_count'] = \App\Models\User::count();
                    $data['verified_users'] = \App\Models\User::whereNotNull('email_verified_at')->count();
                    $data['roles'] = \App\Models\Role::where('guard_name', 'api')->get(['id', 'name', 'organisation_id']);
                    $data['organisations'] = \App\Models\Organisation::all(['id', 'name', 'status']);
                    $data['recent_users'] = \DB::select('SELECT COUNT(*) as total FROM users WHERE created_at > ?', [now()->subDays(30)])[0]->total ?? 0;
            }

            return response()->json([
                'success' => true,
                'message' => "SQL logging test completed - {$testType} mode",
                'test_type' => $testType,
                'data' => $data,
                'request_info' => [
                    'method' => $request->getMethod(),
                    'url' => $request->fullUrl(),
                    'query_params' => $request->query->all(),
                ]
            ]);
        })->name('test-sql-logging');
    }

    // Add more API routes here as needed
});
