<?php

declare(strict_types=1);

namespace App\Services;

use App\Contracts\OrderSyncServiceInterface;
use App\Models\SyncLog;
use App\Models\SyncRecord;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Exception;

final class OrderSyncService implements OrderSyncServiceInterface
{
    private $remoteConnection;
    private $currentBatchId;
    private $syncLog;
    private $config;
    private $progressCallback;

    public function __construct()
    {
        $this->remoteConnection = DB::connection('store');
        $this->config = config('sync');
    }

    /**
     * Execute order synchronization.
     */
    public function sync(array $config = [], ?callable $progressCallback = null): SyncLog
    {
        $this->currentBatchId = $this->generateBatchId();
        $this->progressCallback = $progressCallback;

        Log::info('Order sync started', [
            'batch_id' => $this->currentBatchId,
            'config' => $config,
        ]);

        // Create sync log
        $this->syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => $this->currentBatchId,
            'status' => 'pending',
            'started_at' => now(),
            'sync_config' => $config,
        ]);

        try {
            $this->syncLog->update(['status' => 'processing']);

            $batchSize = (int) ($config['batch_size'] ?? $this->config['batch_size'] ?? 1000);

            // Use optimized streaming approach with preloaded order items
            $this->processOrders($config, $batchSize);

            // Complete sync
            $this->completeSyncLog();

        } catch (Exception $e) {
            $this->handleSyncError($e);
        }

        return $this->syncLog->fresh();
    }

    /**
     * Re-execute sync for specified batch.
     */
    public function reSync(string $batchId): SyncLog
    {
        $originalLog = SyncLog::where('batch_id', $batchId)->firstOrFail();
        $originalConfig = $originalLog->sync_config ?? [];

        Log::info('Re-syncing order batch', [
            'original_batch_id' => $batchId,
            'original_config' => $originalConfig,
        ]);

        return $this->sync($originalConfig);
    }

    /**
     * Get sync statistics.
     */
    public function getSyncStatistics(): array
    {
        $totalSyncs = SyncLog::where('sync_type', 'order_sync')->count();
        $successfulSyncs = SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'completed')
            ->count();
        $failedSyncs = SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'failed')
            ->count();

        $lastSync = SyncLog::where('sync_type', 'order_sync')
            ->orderBy('created_at', 'desc')
            ->first();

        return [
            'total_syncs' => $totalSyncs,
            'successful_syncs' => $successfulSyncs,
            'failed_syncs' => $failedSyncs,
            'success_rate' => $totalSyncs > 0 ? round(($successfulSyncs / $totalSyncs) * 100, 2) : 0,
            'last_sync' => $lastSync ? [
                'batch_id' => $lastSync->batch_id,
                'status' => $lastSync->status,
                'created_at' => $lastSync->created_at->toISOString(),
                'completed_at' => $lastSync->completed_at?->toISOString(),
            ] : null,
        ];
    }

    /**
     * Clean up old sync logs and records.
     */
    public function cleanup(?int $days = null): array
    {
        $days = $days ?? $this->config['log_retention_days'] ?? 30;
        $cutoffDate = now()->subDays($days);

        // Delete old sync records first (foreign key constraint)
        $recordsDeleted = SyncRecord::whereHas('syncLog', function ($query) use ($cutoffDate) {
            $query->where('created_at', '<', $cutoffDate);
        })->delete();

        // Delete old sync logs
        $logsDeleted = SyncLog::where('sync_type', 'order_sync')
            ->where('created_at', '<', $cutoffDate)
            ->delete();

        Log::info('Order sync cleanup completed', [
            'days' => $days,
            'records_deleted' => $recordsDeleted,
            'logs_deleted' => $logsDeleted,
        ]);

        return [
            'records_deleted' => $recordsDeleted,
            'logs_deleted' => $logsDeleted,
            'cutoff_date' => $cutoffDate->toISOString(),
        ];
    }

    /**
     * Execute order synchronization in queue with predefined batch ID.
     */
    public function syncInQueue(array $config = [], ?string $batchId = null, ?callable $progressCallback = null): SyncLog
    {
        $this->currentBatchId = $batchId ?? $this->generateBatchId();
        $this->progressCallback = $progressCallback;

        Log::info('Order sync started in queue', [
            'batch_id' => $this->currentBatchId,
            'config' => $config,
            'queue_mode' => true,
        ]);

        // Create sync log
        $this->syncLog = SyncLog::create([
            'sync_type' => 'order_sync',
            'batch_id' => $this->currentBatchId,
            'status' => 'pending',
            'started_at' => now(),
            'sync_config' => $config,
        ]);

        try {
            $this->syncLog->update(['status' => 'processing']);

            $batchSize = (int) ($config['batch_size'] ?? $this->config['batch_size'] ?? 1000);

            // Use optimized streaming approach with preloaded order items
            $this->processOrders($config, $batchSize);

            // Complete sync
            $this->completeSyncLog();

        } catch (Exception $e) {
            $this->handleSyncError($e);
        }

        return $this->syncLog->fresh();
    }

    /**
     * Re-sync a failed batch in queue.
     */
    public function reSyncInQueue(string $originalBatchId, array $config = [], ?callable $progressCallback = null): SyncLog
    {
        $originalLog = SyncLog::where('batch_id', $originalBatchId)->firstOrFail();
        $originalConfig = $originalLog->sync_config ?? [];

        // For re-sync, use the exact same data range as the original sync
        if (isset($originalConfig['sync_start_time']) && isset($originalConfig['sync_end_time'])) {
            $reSyncConfig = array_merge($originalConfig, $config, [
                'force_time_range' => true,
                'start_time' => $originalConfig['sync_start_time'],
                'end_time' => $originalConfig['sync_end_time'],
            ]);
        } else {
            $reSyncConfig = array_merge($originalConfig, $config);
        }

        return $this->syncInQueue($reSyncConfig, null, $progressCallback);
    }

    /**
     * Check if a full sync has ever been completed successfully.
     */
    public function hasCompletedFullSync(): bool
    {
        return SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'completed')
            ->whereJsonContains('sync_config->sync_type_detail', 'full')
            ->exists();
    }

    /**
     * Check if incremental sync can be performed.
     * Requires at least one successful full sync to have been completed.
     */
    public function canPerformIncrementalSync(): bool
    {
        return $this->hasCompletedFullSync();
    }

    /**
     * Generate a unique batch ID for this sync operation.
     */
    private function generateBatchId(): string
    {
        return 'order_sync_' . now()->format('Y_m_d_H_i_s') . '_' . Str::random(8);
    }

    /**
     * Process orders using optimized approach with preloaded order items and batch database operations.
     */
    private function processOrders(array $config, int $batchSize): void
    {
        $processedCount = 0;
        $totalEstimate = $this->estimateOrderCount($config);
        $startTime = microtime(true);

        // Update sync log with estimated total
        $syncRangeInfo = $this->getSyncRangeInfo($config);
        $this->syncLog->update([
            'total_records' => $totalEstimate,
            'sync_config' => array_merge($config, $syncRangeInfo, ['processing_mode' => 'memory_optimized']),
        ]);

        Log::info('Starting memory-optimized batch order processing', [
            'estimated_total' => $totalEstimate,
            'batch_size' => $batchSize,
            'batch_id' => $this->currentBatchId,
            'memory_usage' => memory_get_usage(true) / 1024 / 1024 . ' MB',
        ]);

        // Balance efficiency and memory usage
        $batchInsertSize = min(
            (int) ($config['batch_insert_size'] ?? $this->config['performance']['batch_insert_size'] ?? 300),
            300 // Cap at 300 for balanced performance and memory management
        );
        $orderBatch = [];
        $batchCount = 0;
        $gcFrequency = 50; // Force garbage collection every 50 records

        // Use streaming approach with preloaded order items and batch processing
        try {
            foreach ($this->getRemoteOrdersWithItems($config, $batchSize) as $remoteOrder) {
                $orderBatch[] = $remoteOrder;
                $processedCount++;

                // Process batch when it reaches the configured size
                if (count($orderBatch) >= $batchInsertSize) {
                    try {
                        $this->processBatchOrders($orderBatch);
                        $orderBatch = []; // Clear the batch array
                        $batchCount++;

                        // Force garbage collection and log memory usage
                        if ($batchCount % 5 === 0) {
                            $this->forceGarbageCollection();
                            $memoryUsage = memory_get_usage(true) / 1024 / 1024;

                            Log::info('Processed batch with memory info', [
                                'batch_number' => $batchCount,
                                'processed_count' => $processedCount,
                                'batch_id' => $this->currentBatchId,
                                'progress_percentage' => round(($processedCount / $totalEstimate) * 100, 2),
                                'memory_usage_mb' => round($memoryUsage, 2),
                                'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                            ]);
                        }
                    } catch (Exception $e) {
                        Log::error('Batch processing failed', [
                            'batch_number' => $batchCount + 1,
                            'processed_count' => $processedCount,
                            'batch_id' => $this->currentBatchId,
                            'error' => $e->getMessage(),
                            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
                        ]);

                        // Re-throw the exception to stop processing
                        throw $e;
                    }
                }

                // Periodic garbage collection and memory monitoring for individual records
                if ($processedCount % $gcFrequency === 0) {
                    $this->forceGarbageCollection();
                    $this->checkMemoryUsage();
                }

                // Report progress periodically
                if ($processedCount % 100 === 0) {
                    if ($this->progressCallback) {
                        $elapsedTime = microtime(true) - $startTime;
                        call_user_func($this->progressCallback, $processedCount, $totalEstimate, $elapsedTime);
                    }

                    // Update sync log with current progress
                    $this->syncLog->update([
                        'processed_records' => $processedCount,
                    ]);

                    // Force garbage collection periodically
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }
            }
        } catch (Exception $e) {
            Log::error('Order streaming failed', [
                'processed_count' => $processedCount,
                'batch_id' => $this->currentBatchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Update sync log with current progress before re-throwing
            $this->syncLog->update([
                'processed_records' => $processedCount,
            ]);

            throw $e;
        }

        // Process remaining orders in the final batch
        if (!empty($orderBatch)) {
            try {
                $this->processBatchOrders($orderBatch);
                $batchCount++;

                Log::info('Processed final batch', [
                    'batch_number' => $batchCount,
                    'final_batch_size' => count($orderBatch),
                    'processed_count' => $processedCount,
                    'batch_id' => $this->currentBatchId,
                ]);
            } catch (Exception $e) {
                Log::error('Final batch processing failed', [
                    'batch_number' => $batchCount + 1,
                    'final_batch_size' => count($orderBatch),
                    'processed_count' => $processedCount,
                    'batch_id' => $this->currentBatchId,
                    'error' => $e->getMessage(),
                ]);

                // Update sync log with current progress before re-throwing
                $this->syncLog->update([
                    'processed_records' => $processedCount,
                ]);

                throw $e;
            }
        }

        Log::info('Batch processing loop completed', [
            'processed_count' => $processedCount,
            'total_batches' => $batchCount,
            'batch_id' => $this->currentBatchId,
            'estimated_total' => $totalEstimate,
            'actual_vs_estimated_diff' => $processedCount - $totalEstimate,
        ]);

        // Update final count - use actual processed count instead of estimate
        $this->syncLog->update([
            'total_records' => $processedCount,
            'processed_records' => $processedCount,
        ]);

        Log::info('Optimized batch order processing completed', [
            'processed_count' => $processedCount,
            'total_batches' => $batchCount,
            'batch_id' => $this->currentBatchId,
            'estimated_total' => $totalEstimate,
            'count_accuracy' => $processedCount === $totalEstimate ? 'accurate' : 'adjusted',
        ]);
    }



    /**
     * Process a batch of orders using bulk database operations.
     */
    private function processBatchOrders(array $remoteOrders): void
    {
        $orderData = [];
        $orderItemsData = [];
        $syncRecords = [];
        $orderIdMapping = [];

        // Prepare data for batch operations
        foreach ($remoteOrders as $remoteOrder) {
            try {
                // Transform order data
                $transformedOrderData = $this->transformOrderData($remoteOrder);

                // Check if update is needed (based on order_updated_at)
                $existingOrder = Order::where('store_order_id', $remoteOrder['order_id'])->first();
                $shouldSkip = false;

                if ($existingOrder) {
                    $remoteUpdateTime = $remoteOrder['order_updated_at'] ?? '1970-01-01 00:00:00';
                    $localUpdateTime = $existingOrder->store_updated_at?->format('Y-m-d H:i:s') ?? '1970-01-01 00:00:00';

                    if ($localUpdateTime >= $remoteUpdateTime) {
                        // Data not updated, skip
                        $shouldSkip = true;
                    }
                }

                if (!$shouldSkip) {
                    // Add timestamps for bulk insert
                    $transformedOrderData['created_at'] = now();
                    $transformedOrderData['updated_at'] = now();
                    $orderData[] = $transformedOrderData;

                    // Store mapping for order items processing
                    $orderIdMapping[$remoteOrder['order_id']] = count($orderData) - 1;
                }

                // Prepare sync record
                $syncRecords[] = [
                    'batch_id' => $this->currentBatchId,
                    'source_table' => 'sylius_order',
                    'source_id' => (string) $remoteOrder['order_id'],
                    'target_table' => 'orders',
                    'status' => $shouldSkip ? 'skipped' : 'pending',
                    'source_data' => json_encode($remoteOrder),
                    'transformed_data' => json_encode($transformedOrderData),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

            } catch (Exception $e) {
                Log::error('Order preparation failed', [
                    'order_id' => $remoteOrder['order_id'],
                    'error' => $e->getMessage(),
                ]);

                // Add failed sync record
                $syncRecords[] = [
                    'batch_id' => $this->currentBatchId,
                    'source_table' => 'sylius_order',
                    'source_id' => (string) $remoteOrder['order_id'],
                    'target_table' => 'orders',
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'source_data' => json_encode($remoteOrder),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        // Execute batch database operations
        if (!empty($orderData) || !empty($syncRecords)) {
            DB::transaction(function () use ($orderData, $orderItemsData, $syncRecords, $remoteOrders, $orderIdMapping) {
                $insertedOrderIds = [];

                // Batch upsert orders
                if (!empty($orderData)) {
                    $insertedOrderIds = $this->bulkUpsertOrders($orderData);
                }

                // Prepare and insert order items
                if (!empty($insertedOrderIds)) {
                    $this->prepareAndInsertOrderItems($remoteOrders, $insertedOrderIds, $orderIdMapping);
                }

                // Batch insert sync records
                if (!empty($syncRecords)) {
                    $this->updateSyncRecordsWithResults($syncRecords, $insertedOrderIds, $orderIdMapping);
                    SyncRecord::insert($syncRecords);
                }
            });
        }
    }

    /**
     * Estimate total order count for progress tracking.
     * Uses a separate count query without JOINs to avoid inflated counts.
     */
    private function estimateOrderCount(array $config): int
    {
        $query = $this->remoteConnection->table('sylius_order as o')
            ->whereNotNull('o.checkout_completed_at'); // Only sync completed orders

        // Handle time-based filtering (same logic as buildBaseOrderQuery but without JOINs)
        if (isset($config['force_time_range']) && $config['force_time_range']) {
            // For re-sync: use exact time range from original sync
            $startTime = $config['start_time'];
            $endTime = $config['end_time'];
            $query->whereBetween('o.checkout_completed_at', [$startTime, $endTime]);
        } elseif (isset($config['incremental']) && $config['incremental']) {
            // Incremental sync: only get updated data since last successful sync
            $lastSyncTime = $this->getLastSuccessfulSyncTime();
            if ($lastSyncTime) {
                $query->where(function($q) use ($lastSyncTime) {
                    $q->where('o.updated_at', '>', $lastSyncTime)
                      ->orWhere('o.checkout_completed_at', '>', $lastSyncTime);
                });
            }
        } elseif (isset($config['start_date']) || isset($config['end_date'])) {
            // Custom date range
            if (isset($config['start_date'])) {
                $query->where('o.checkout_completed_at', '>=', $config['start_date']);
            }
            if (isset($config['end_date'])) {
                $query->where('o.checkout_completed_at', '<=', $config['end_date']);
            }
        }

        return $query->count();
    }

    /**
     * Bulk upsert orders using raw SQL for better performance.
     */
    private function bulkUpsertOrders(array $orderData): array
    {
        if (empty($orderData)) {
            return [];
        }

        $tableName = (new Order())->getTable();
        $columns = array_keys($orderData[0]);
        $placeholders = '(' . implode(',', array_fill(0, count($columns), '?')) . ')';
        $values = [];

        foreach ($orderData as $order) {
            $values = array_merge($values, array_values($order));
        }

        // Columns that should be updated on duplicate key (exclude unique identifiers and created_at)
        $updateColumns = array_filter($columns, fn($col) => !in_array($col, ['store_order_id', 'created_at']));
        $updateClause = implode(',', array_map(fn($col) => "`{$col}` = VALUES(`{$col}`)", $updateColumns));

        $sql = "INSERT INTO `{$tableName}` (`" . implode('`,`', $columns) . "`) VALUES "
             . implode(',', array_fill(0, count($orderData), $placeholders))
             . " ON DUPLICATE KEY UPDATE {$updateClause}";

        DB::statement($sql, $values);

        // Get the inserted/updated order IDs
        $storeOrderIds = array_column($orderData, 'store_order_id');
        $orders = Order::whereIn('store_order_id', $storeOrderIds)->get(['id', 'store_order_id']);

        $orderIdMap = [];
        foreach ($orders as $order) {
            $orderIdMap[$order->store_order_id] = $order->id;
        }

        return $orderIdMap;
    }

    /**
     * Prepare and insert order items in batch.
     */
    private function prepareAndInsertOrderItems(array $remoteOrders, array $orderIdMap, array $orderIdMapping): void
    {
        $orderItemsData = [];
        $orderIdsToCleanup = [];

        foreach ($remoteOrders as $remoteOrder) {
            $storeOrderId = $remoteOrder['order_id'];

            if (!isset($orderIdMap[$storeOrderId])) {
                continue; // Order was skipped or failed
            }

            $localOrderId = $orderIdMap[$storeOrderId];
            $orderIdsToCleanup[] = $localOrderId;

            // Get order items for processing
            $remoteOrderItems = $this->getOrderItemsForProcessing($remoteOrder);

            // Prepare order items data
            foreach ($remoteOrderItems as $remoteItem) {
                $transformedItemData = $this->transformOrderItemData($remoteItem, $localOrderId);
                $transformedItemData['created_at'] = now();
                $transformedItemData['updated_at'] = now();
                $orderItemsData[] = $transformedItemData;
            }
        }

        // Clean up existing order items for updated orders
        if (!empty($orderIdsToCleanup)) {
            OrderItem::whereIn('order_id', $orderIdsToCleanup)->delete();
        }

        // Batch insert new order items
        if (!empty($orderItemsData)) {
            // Split into smaller chunks to avoid hitting MySQL limits
            $chunks = array_chunk($orderItemsData, 1000);
            foreach ($chunks as $chunk) {
                OrderItem::insert($chunk);
            }
        }
    }

    /**
     * Update sync records with processing results.
     */
    private function updateSyncRecordsWithResults(array &$syncRecords, array $orderIdMap, array $orderIdMapping): void
    {
        foreach ($syncRecords as &$record) {
            if ($record['status'] === 'pending') {
                $storeOrderId = (int) $record['source_id'];

                if (isset($orderIdMap[$storeOrderId])) {
                    $record['status'] = 'success';
                    $record['target_id'] = (string) $orderIdMap[$storeOrderId];
                } else {
                    $record['status'] = 'failed';
                    $record['error_message'] = 'Order not found after bulk insert';
                }
            }
        }
    }

    /**
     * Get last successful sync time.
     */
    private function getLastSuccessfulSyncTime(): ?string
    {
        $lastSync = SyncLog::where('sync_type', 'order_sync')
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->first();

        return $lastSync ? $lastSync->completed_at->format('Y-m-d H:i:s') : null;
    }



    /**
     * Get remote orders with preloaded order items using streaming approach.
     */
    private function getRemoteOrdersWithItems(array $config, int $batchSize = 1000): \Generator
    {
        $query = $this->buildBaseOrderQuery($config);
        $offset = 0;

        // Reduce batch size for better memory management
        $memoryOptimizedBatchSize = min($batchSize, 500);

        do {
            $orders = $query->offset($offset)->limit($memoryOptimizedBatchSize)->get();

            if ($orders->isEmpty()) {
                break;
            }

            $orderIds = $orders->pluck('order_id')->toArray();
            $orderCount = $orders->count();

            // Batch load all order items for this chunk
            $orderItems = $this->getOrderItemsBatch($orderIds);

            // Group order items by order_id for efficient lookup
            $itemsByOrderId = $orderItems->groupBy('order_id');

            foreach ($orders as $order) {
                // Convert order to array if needed
                if (is_object($order)) {
                    $order = (array) $order;
                }

                // Attach preloaded order items (convert objects to arrays)
                $items = $itemsByOrderId->get($order['order_id'], collect())->toArray();
                $order['preloaded_items'] = array_map(function($item) {
                    return is_object($item) ? (array) $item : $item;
                }, $items);

                yield $order;
            }

            // Clear variables to free memory
            unset($orders, $orderIds, $orderItems, $itemsByOrderId);

            $offset += $memoryOptimizedBatchSize;
        } while ($orderCount === $memoryOptimizedBatchSize);
    }

    /**
     * Force garbage collection to free memory.
     */
    private function forceGarbageCollection(): void
    {
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        // Additional memory cleanup
        if (function_exists('gc_mem_caches')) {
            gc_mem_caches();
        }
    }

    /**
     * Get current memory usage information.
     */
    private function getMemoryUsageInfo(): array
    {
        return [
            'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'limit' => ini_get('memory_limit'),
        ];
    }

    /**
     * Check if memory usage is approaching the limit and log warning.
     */
    private function checkMemoryUsage(): void
    {
        $memoryInfo = $this->getMemoryUsageInfo();
        $memoryLimitBytes = $this->parseMemoryLimit($memoryInfo['limit']);
        $currentBytes = memory_get_usage(true);

        $usagePercentage = ($currentBytes / $memoryLimitBytes) * 100;

        if ($usagePercentage > 80) {
            Log::warning('High memory usage detected', [
                'batch_id' => $this->currentBatchId,
                'memory_info' => $memoryInfo,
                'usage_percentage' => round($usagePercentage, 2),
            ]);
        }
    }

    /**
     * Parse memory limit string to bytes.
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Build base order query with common filters and joins.
     */
    private function buildBaseOrderQuery(array $config)
    {
        // Create subquery for refund data to avoid duplicates when orders have multiple refund records
        $refundSubquery = $this->remoteConnection->table('bitbag_refund_credit_memo as rcm_sub')
            ->select([
                'rcm_sub.order_id',
                'rcm_sub.total as refund_total',
                'rcm_sub.comment as refund_comment',
                'rcm_sub.status as refund_status',
                'rcm_sub.created_at as refunded_at'
            ])
            ->where('rcm_sub.status', '=', 'success')
            ->whereRaw('rcm_sub.id = (
                SELECT MIN(rcm_inner.id)
                FROM bitbag_refund_credit_memo rcm_inner
                WHERE rcm_inner.order_id = rcm_sub.order_id
                AND rcm_inner.status = "success"
            )'); // Get only the first successful refund record per order

        $query = $this->remoteConnection->table('sylius_order as o')
            ->leftJoin('sylius_address as sa', 'o.shipping_address_id', '=', 'sa.id')
            ->leftJoinSub($refundSubquery, 'rcm', function($join) {
                $join->on('o.id', '=', 'rcm.order_id');
            })
            ->select([
                'o.id as order_id',
                'o.number as order_number',
                'o.state',
                'o.checkout_completed_at',
                'o.items_total',
                'o.adjustments_total',
                'o.total',
                'o.currency_code',
                'o.payment_state',
                'o.customer_id',
                'o.updated_at as order_updated_at',
                'sa.country_code as shipping_country',
                'rcm.refund_total',
                'rcm.refund_comment',
                'rcm.refund_status',
                'rcm.refunded_at'
            ])
            ->whereNotNull('o.checkout_completed_at'); // Only sync completed orders

        // Handle time-based filtering
        if (isset($config['force_time_range']) && $config['force_time_range']) {
            // For re-sync: use exact time range from original sync
            $startTime = $config['start_time'];
            $endTime = $config['end_time'];
            $query->whereBetween('o.checkout_completed_at', [$startTime, $endTime]);
        } elseif (isset($config['incremental']) && $config['incremental']) {
            // Incremental sync: only get updated data since last successful sync
            $lastSyncTime = $this->getLastSuccessfulSyncTime();
            if ($lastSyncTime) {
                $query->where(function($q) use ($lastSyncTime) {
                    $q->where('o.updated_at', '>', $lastSyncTime)
                      ->orWhere('o.checkout_completed_at', '>', $lastSyncTime);
                });
            }
        } elseif (isset($config['start_date']) || isset($config['end_date'])) {
            // Custom date range
            if (isset($config['start_date'])) {
                $query->where('o.checkout_completed_at', '>=', $config['start_date']);
            }
            if (isset($config['end_date'])) {
                $query->where('o.checkout_completed_at', '<=', $config['end_date']);
            }
        }

        // Add ordering for consistent results
        $query->orderBy('o.checkout_completed_at', 'asc')
              ->orderBy('o.id', 'asc');

        return $query;
    }

    /**
     * Process a single order and its items.
     */
    private function processOrder(array $remoteOrder): void
    {
        $syncRecord = SyncRecord::create([
            'batch_id' => $this->currentBatchId,
            'source_table' => 'sylius_order',
            'source_id' => (string) $remoteOrder['order_id'],
            'target_table' => 'orders',
            'status' => 'pending',
            'source_data' => $remoteOrder,
        ]);

        try {
            // Transform order data
            $transformedOrderData = $this->transformOrderData($remoteOrder);

            // Check if update is needed (based on order_updated_at)
            $existingOrder = Order::where('store_order_id', $remoteOrder['order_id'])->first();

            if ($existingOrder) {
                $remoteUpdateTime = $remoteOrder['order_updated_at'] ?? '1970-01-01 00:00:00';
                $localUpdateTime = $existingOrder->store_updated_at?->format('Y-m-d H:i:s') ?? '1970-01-01 00:00:00';

                if ($localUpdateTime >= $remoteUpdateTime) {
                    // Data not updated, skip
                    $syncRecord->update([
                        'status' => 'skipped',
                        'transformed_data' => $transformedOrderData,
                    ]);
                    return;
                }
            }

            // Save or update order
            $order = Order::updateOrCreate(
                ['store_order_id' => $remoteOrder['order_id']],
                $transformedOrderData
            );

            // Get and process order items - use preloaded data if available
            $remoteOrderItems = $this->getOrderItemsForProcessing($remoteOrder);

            // Delete existing order items to avoid duplicates
            if ($existingOrder) {
                OrderItem::where('order_id', $order->id)->delete();
            }

            // Process each order item
            foreach ($remoteOrderItems as $remoteItem) {
                $transformedItemData = $this->transformOrderItemData($remoteItem, $order->id);
                OrderItem::create($transformedItemData);
            }

            $syncRecord->update([
                'status' => 'success',
                'target_id' => (string) $order->id,
                'transformed_data' => $transformedOrderData,
            ]);

        } catch (Exception $e) {
            $syncRecord->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);

            Log::error('Order sync failed', [
                'batch_id' => $this->currentBatchId,
                'order_id' => $remoteOrder['order_id'],
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get order items for processing - use preloaded data if available.
     */
    private function getOrderItemsForProcessing(array $remoteOrder): array
    {
        // Check if order has preloaded items (from batch processing)
        if (isset($remoteOrder['preloaded_items']) && is_array($remoteOrder['preloaded_items'])) {
            return $remoteOrder['preloaded_items'];
        }

        // Fallback to individual query for backward compatibility
        return $this->getOrderItems($remoteOrder['order_id']);
    }

    /**
     * Get order items for a specific order.
     */
    private function getOrderItems(int $orderId): array
    {
        $items = $this->remoteConnection->table('sylius_order_item as oi')
            ->select([
                'oi.id as item_id',
                'oi.variant_id',
                'oi.quantity',
                'oi.unit_price',
                'oi.units_total',
                'oi.adjustments_total',
                'oi.total',
                'oi.product_name',
                'oi.variant_name',
                'oi.quantity_refunded'
            ])
            ->where('oi.order_id', $orderId)
            ->get()
            ->toArray();

        // Convert objects to arrays if needed
        foreach ($items as &$item) {
            if (is_object($item)) {
                $item = (array) $item;
            }
        }

        return $items;
    }

    /**
     * Get order items for multiple orders in batch to avoid N+1 queries.
     */
    private function getOrderItemsBatch(array $orderIds): Collection
    {
        if (empty($orderIds)) {
            return collect();
        }

        return $this->remoteConnection->table('sylius_order_item as oi')
            ->select([
                'oi.order_id',
                'oi.id as item_id',
                'oi.variant_id',
                'oi.quantity',
                'oi.unit_price',
                'oi.units_total',
                'oi.adjustments_total',
                'oi.total',
                'oi.product_name',
                'oi.variant_name',
                'oi.quantity_refunded'
            ])
            ->whereIn('oi.order_id', $orderIds)
            ->get();
    }

    /**
     * Get sync range information for logging.
     */
    private function getSyncRangeInfo(array $config): array
    {
        $rangeInfo = [];

        if (isset($config['force_time_range']) && $config['force_time_range']) {
            // For re-sync: use the provided time range
            $rangeInfo['sync_start_time'] = $config['start_time'];
            $rangeInfo['sync_end_time'] = $config['end_time'];
            $rangeInfo['sync_type_detail'] = 'resync';
        } elseif (isset($config['incremental']) && $config['incremental']) {
            // For incremental sync, record the time range being synced
            $lastSyncTime = $this->getLastSuccessfulSyncTime();
            $currentTime = now()->format('Y-m-d H:i:s');

            $rangeInfo['sync_start_time'] = $lastSyncTime ?: '1970-01-01 00:00:00';
            $rangeInfo['sync_end_time'] = $currentTime;
            $rangeInfo['sync_type_detail'] = 'incremental';
        } elseif (isset($config['start_date']) || isset($config['end_date'])) {
            // For custom date range
            $rangeInfo['sync_start_time'] = $config['start_date'] ?? '1970-01-01 00:00:00';
            $rangeInfo['sync_end_time'] = $config['end_date'] ?? now()->format('Y-m-d H:i:s');
            $rangeInfo['sync_type_detail'] = 'custom_range';
        } else {
            // For full sync, record that it was a full sync at this time
            $rangeInfo['sync_start_time'] = '1970-01-01 00:00:00';
            $rangeInfo['sync_end_time'] = now()->format('Y-m-d H:i:s');
            $rangeInfo['sync_type_detail'] = 'full';
        }

        return $rangeInfo;
    }

    /**
     * Transform order data from Sylius format to local format.
     */
    private function transformOrderData(array $remoteOrder): array
    {
        return [
            'store_order_id' => $remoteOrder['order_id'],
            'order_number' => $remoteOrder['order_number'],
            'state' => $this->mapOrderState($remoteOrder['state']),
            'completed_at' => $remoteOrder['checkout_completed_at'] ? now()->parse($remoteOrder['checkout_completed_at']) : null,
            'items_total' => $this->convertToMinorUnits($remoteOrder['items_total']),
            'adjustments_total' => $this->convertToMinorUnits($remoteOrder['adjustments_total']),
            'total_amount' => $this->convertToMinorUnits($remoteOrder['total']),
            'currency_code' => $remoteOrder['currency_code'],
            'payment_state' => $remoteOrder['payment_state'],
            'shipping_country' => $remoteOrder['shipping_country'],
            'customer_id' => $remoteOrder['customer_id'],
            'refund_total' => $remoteOrder['refund_total'] ? $this->convertToMinorUnits($remoteOrder['refund_total']) : null,
            'refund_comment' => $remoteOrder['refund_comment'] ? substr($remoteOrder['refund_comment'], 0, 255) : null,
            'refund_status' => $remoteOrder['refund_status'],
            'refunded_at' => $remoteOrder['refunded_at'] ? now()->parse($remoteOrder['refunded_at']) : null,
            'store_updated_at' => $remoteOrder['order_updated_at'] ? now()->parse($remoteOrder['order_updated_at']) : null,
        ];
    }

    /**
     * Transform order item data from Sylius format to local format.
     */
    private function transformOrderItemData(array $remoteItem, int $localOrderId): array
    {
        return [
            'order_id' => $localOrderId,
            'store_order_item_id' => $remoteItem['item_id'],
            'store_variant_id' => $remoteItem['variant_id'],
            'product_name' => $remoteItem['product_name'],
            'variant_name' => $remoteItem['variant_name'],
            'quantity' => $remoteItem['quantity'],
            'unit_price' => $this->convertToMinorUnits($remoteItem['unit_price']),
            'units_total' => $this->convertToMinorUnits($remoteItem['units_total']),
            'adjustments_total' => $this->convertToMinorUnits($remoteItem['adjustments_total']),
            'total' => $this->convertToMinorUnits($remoteItem['total']),
            'quantity_refunded' => $remoteItem['quantity_refunded'] ?? 0,
        ];
    }

    /**
     * Map Sylius order state to local order state.
     */
    private function mapOrderState(string $syliusState): string
    {
        $stateMapping = [
            'cart' => 'cart',
            'new' => 'new',
            'fulfilled' => 'completed',
            'cancelled' => 'cancelled',
        ];

        return $stateMapping[$syliusState] ?? $syliusState;
    }

    /**
     * Convert monetary amount to minor units (cents).
     */
    private function convertToMinorUnits($amount): int
    {
        if ($amount === null) {
            return 0;
        }

        // Sylius stores amounts in minor units (cents), so we just need to ensure it's an integer
        return (int) $amount;
    }

    /**
     * Complete the sync log with success status.
     */
    private function completeSyncLog(): void
    {
        $records = SyncRecord::where('batch_id', $this->currentBatchId)->get();

        $summary = [
            'total' => $records->count(),
            'success' => $records->where('status', 'success')->count(),
            'failed' => $records->where('status', 'failed')->count(),
            'skipped' => $records->where('status', 'skipped')->count(),
        ];

        $this->syncLog->update([
            'status' => 'completed',
            'completed_at' => now(),
            'processed_records' => $summary['total'],
            'success_records' => $summary['success'],
            'failed_records' => $summary['failed'],
            'summary' => $summary,
        ]);

        Log::info('Order sync completed successfully', [
            'batch_id' => $this->currentBatchId,
            'summary' => $summary,
        ]);
    }

    /**
     * Handle sync errors and update log status.
     */
    private function handleSyncError(Exception $e): void
    {
        $this->syncLog->update([
            'status' => 'failed',
            'error_message' => $e->getMessage(),
            'completed_at' => now(),
        ]);

        Log::error('Order sync failed', [
            'batch_id' => $this->currentBatchId,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
    }
}
