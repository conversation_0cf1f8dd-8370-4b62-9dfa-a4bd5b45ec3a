<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

final class OrganisationFilterTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private string $token;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Give the user system admin role for organisation management
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system', // System-wide role requires 'system' guard
            'organisation_id' => null, // System-wide role
        ]);

        // Set the user's guard to system and assign the role
        $this->user->guard_name = 'system';
        $this->user->assignRole($adminRole);

        $this->token = $this->user->createToken('test-token')->plainTextToken;
    }

    public function test_can_filter_organisations_by_ids(): void
    {
        // Create test organisations
        $org1 = Organisation::factory()->create(['name' => 'Test Org 1', 'code' => 'TEST001']);
        $org2 = Organisation::factory()->create(['name' => 'Test Org 2', 'code' => 'TEST002']);
        $org3 = Organisation::factory()->create(['name' => 'Test Org 3', 'code' => 'TEST003']);
        $org4 = Organisation::factory()->create(['name' => 'Other Org', 'code' => 'OTHER001']);

        // Test filtering by specific IDs
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations?organization_ids=' . $org1->id . ',' . $org3->id);

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.total', 2)
            ->assertJsonPath('data.data.0.id', $org1->id)
            ->assertJsonPath('data.data.1.id', $org3->id);
    }

    public function test_can_search_within_filtered_ids(): void
    {
        // Create test organisations
        $org1 = Organisation::factory()->create(['name' => 'Test Company A', 'code' => 'TEST001']);
        $org2 = Organisation::factory()->create(['name' => 'Test Company B', 'code' => 'TEST002']);
        $org3 = Organisation::factory()->create(['name' => 'Sample Corp', 'code' => 'SAMPLE001']);
        $org4 = Organisation::factory()->create(['name' => 'Test Company C', 'code' => 'TEST003']);

        // Search for "Test" within specific IDs
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations?search=Test&organization_ids=' . $org1->id . ',' . $org3->id . ',' . $org4->id);

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.total', 2); // Should find org1 and org4, but not org3 (doesn't match "Test")

        $returnedIds = collect($response->json('data.data'))->pluck('id')->toArray();
        $this->assertContains($org1->id, $returnedIds);
        $this->assertContains($org4->id, $returnedIds);
        $this->assertNotContains($org3->id, $returnedIds);
    }

    public function test_empty_organization_ids_returns_all_organisations(): void
    {
        // Create test organisations
        Organisation::factory()->count(5)->create();

        // Test with empty organization_ids parameter
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations?organization_ids=');

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.total', 5);
    }

    public function test_invalid_organization_ids_returns_empty_result(): void
    {
        // Create test organisations
        Organisation::factory()->count(3)->create();

        // Test with non-existent IDs
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations?organization_ids=999,998,997');

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.total', 0);
    }
}
